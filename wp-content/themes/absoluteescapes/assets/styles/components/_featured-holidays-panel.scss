// Featured Holidays Panel Component Styles

.featured-holidays-panel {
    padding: 50px 0;
    background-color: transparent;

    &__inner {
        // AOS fade animation support
    }

    &__container {
        // Full width container
        max-width: none;
        padding: 0;
    }

    &__content {
        // Full width content
        max-width: none;
        margin: 0;
        padding: 0 15px; // Small padding for mobile

        @media (min-width: 768px) {
            padding: 0 30px;
        }
    }

    &__heading {
        text-align: center;
        max-width: 1200px;
        margin: 0 auto 50px auto;
    }

    &__grid {
        display: grid;
        gap: 10px;
        width: 100%;

        // Dynamic grid based on number of items
        &[data-count="1"] {
            grid-template-columns: 1fr;
            max-width: 400px;
            margin: 0 auto;
        }

        &[data-count="2"] {
            grid-template-columns: repeat(2, 1fr);
        }

        &[data-count="3"] {
            grid-template-columns: repeat(3, 1fr);
        }

        &[data-count="4"] {
            grid-template-columns: repeat(4, 1fr);
        }

        // Responsive adjustments
        @media (max-width: 767px) {
            grid-template-columns: 1fr !important;
            gap: 10px;
        }

        @media (min-width: 768px) and (max-width: 991px) {
            &[data-count="3"],
            &[data-count="4"] {
                grid-template-columns: repeat(2, 1fr);
            }
        }
    }

    &__tile {
        position: relative;
        border-radius: 0; // Removed border radius
        overflow: hidden;
        box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
        transition: transform 0.3s ease, box-shadow 0.3s ease;

        &:hover {
            transform: translateY(-5px);
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);

            .featured-holidays-panel__overlay {
                background: linear-gradient(to bottom, rgba(0, 0, 0, 0) 0%, rgba(0, 0, 0, 0) 75%, rgba(0, 0, 0, 0.8) 100%);
            }

            .featured-holidays-panel__button {
                background-color: #2c5f5f;
            }
        }
    }

    &__link {
        display: block;
        text-decoration: none;
        color: inherit;
        height: 100%;
    }

    &__image {
        position: relative;
        height: 500px;
        background-size: cover;
        background-position: center;
        background-repeat: no-repeat;

        @media (max-width: 767px) {
            height: 250px;
        }
    }

    &__overlay {
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: linear-gradient(to bottom, rgba(0, 0, 0, 0) 0%, rgba(0, 0, 0, 0) 75%, rgba(0, 0, 0, 0.7) 100%);
        display: flex;
        align-items: flex-end;
        padding: 20px;
        transition: background 0.3s ease;
    }

    &__content-wrapper {
        width: 100%;
        color: white;
    }

    &__title {
        font-size: 1.25rem;
        font-weight: 600;
        margin-bottom: 8px;
        line-height: 1.3;
        color: white;
    }

    &__price {
        margin-bottom: 12px;

        span {
            font-size: 24px;
            font-weight: 500;
            color: rgba(255, 255, 255, 0.9);
        }
    }

    &__types {
        position: absolute;
        bottom: 20px;
        right: 20px;
        display: flex;
        gap: 8px;
        margin-bottom: 0;
        flex-wrap: wrap;
    }

    &__type {
        // Individual type styling
    }

    &__icon {
        display: inline-flex;
        align-items: center;
        justify-content: center;
        width: 40px;
        height: 40px;
        border-radius: 50%;
        background-color: #3e5056;

        img {
            width: 20px;
            height: 20px;
            filter: brightness(0) invert(1);
        }

        svg {
            width: 22px;
            height: 22px;
        }

        i {
            font-size: 18px;
            color: white;
        }
    }

    &__cta {
        margin-top: auto;
    }

    &__button {
        display: inline-flex;
        align-items: center;
        gap: 8px;
        padding: 8px 16px;
        background-color: rgba(44, 95, 95, 0.9);
        color: white;
        border-radius: 4px;
        font-size: 0.9rem;
        font-weight: 500;
        transition: background-color 0.3s ease;
        border: none;
        text-decoration: none;

        i {
            font-size: 0.8rem;
            transition: transform 0.3s ease;
        }
    }

    // Responsive adjustments
    @media (max-width: 767px) {
        padding: 30px 0;

        &__heading {
            margin-bottom: 30px;
            font-size: 1.8rem;
        }

        &__grid {
            gap: 20px;
            grid-template-columns: 1fr;
        }

        &__overlay {
            padding: 15px;
        }

        &__title {
            font-size: 1.1rem;
        }

        &__price {
            span {
                font-size: 18px;
            }
        }

        &__types {
            bottom: 15px;
            right: 15px;
        }

        &__icon {
            width: 32px;
            height: 32px;

            img {
                width: 16px;
                height: 16px;
            }

            svg {
                width: 18px;
                height: 18px;
            }

            i {
                font-size: 14px;
            }
        }
    }
}

// Section heading for "All holidays"
.holidays-results__section-heading {
    margin-bottom: 30px;
    text-align: center;

    .holidays-results__heading {
        margin-bottom: 0;
    }
}

// Specific selector for h5 title elements
h5.featured-holidays-panel__title {
    font-size: 32px;

    @media (max-width: 767px) {
        font-size: 24px;
    }
}
